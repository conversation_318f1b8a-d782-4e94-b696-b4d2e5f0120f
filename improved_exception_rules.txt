### 例外情况 (极少数)
- 仅当@desktop-commander明确报错、不可用或功能不支持时，才使用基础工具
- 使用基础工具时必须在回复中说明原因和具体的不可用情况
- **文件内容编辑操作**: 当需要修改文件中的特定内容时，自动使用 str-replace-editor 而非 desktop-commander 的编辑功能
- **文件内容追加操作**: 当需要在文件末尾或指定位置追加内容时，自动使用 str-replace-editor 的 insert 命令而非 desktop-commander 的追加功能

#### 自动识别规则
- **编辑场景识别**: 当任务涉及"修改文件内容"、"替换文本"、"更新代码"等编辑操作时，自动选择 str-replace-editor
- **追加场景识别**: 当任务涉及"添加内容到文件"、"追加文本"、"在文件末尾插入"等追加操作时，自动选择 str-replace-editor 的 insert 命令
- **操作意图判断**: AI应根据用户的操作意图自动判断是否属于编辑或追加场景，无需用户明确指定工具名称

#### 基础工具使用方法
**str-replace-editor 编辑**:
- old_str_1: 要替换的原始文本
- new_str_1: 新的替换文本  
- old_str_start_line_number_1: 起始行号
- old_str_end_line_number_1: 结束行号

**str-replace-editor 追加**:
- insert_line_1: 插入位置行号（在此行后插入）
- new_str_1: 要插入的内容
- 追加到文件末尾：使用文件最后一行号作为insert_line_1
